using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using DG.Tweening;
using System;

public class Tractor : MonoBehaviour
{
    public Transform Seed, trailer;

    [Header("Fade Screen")]
    public Image fadeScreen; // Assign a UI Image for fade effect
    public float fadeDuration = 0.5f;
    public GameObject[] firstcheck, lastcheck;
    private bool isCheckPointActive = true;
    public GameObject cutscene, rcccam, canvas, complete;
    public Text instructiontext;
    public String[] Triggertext;
    public String[] Levelwise;
    public GameObject instruction;
    public void OnTriggerEnter(Collider collision)
    {
        if (collision.gameObject.tag == "checkpoint")
        {
            collision.gameObject.GetComponent<BoxCollider>().enabled = false;
            collision.gameObject.transform.GetChild(0).gameObject.SetActive(false);
            collision.gameObject.transform.GetChild(1).gameObject.SetActive(true);
        }
        else if (collision.gameObject.tag == "finishline")
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            collision.gameObject.SetActive(false);
            Tructorlinkcontroll.instance.SmoothLinkDown();
            StartCoroutine(Finish());
            instructiontext.text = Levelwise[MainMenu.levlno];
        }
        else if (collision.gameObject.tag == "linkSeedM")
        {
            this.GetComponent<Rigidbody>().drag = 10f;
            RCC_Camera.instance.TPSDistance = 35;
            RCC_Camera.instance.TPSHeight = 7;
            instructiontext.text = Triggertext[0];
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in lastcheck)
            {
                obj.SetActive(true);
            }
            collision.gameObject.SetActive(false);
            StartCoroutine(ResetTractorPosition());

        }
        else if (collision.gameObject.tag == "trailerlink")
        {
            instructiontext.text = Triggertext[0];
            this.GetComponent<Rigidbody>().drag = 10f;
            RCC_Camera.instance.TPSDistance = 40;
            RCC_Camera.instance.TPSHeight = 7;
            foreach (GameObject obj in firstcheck)
            {
                obj.SetActive(false);
            }
            foreach (GameObject obj in lastcheck)
            {
                obj.SetActive(true);
            }

            collision.gameObject.SetActive(false);
            StartCoroutine(ResetTractorPosition1());
        }
        else if (collision.gameObject.tag == "finish")
        {
            StartCoroutine(HandleFinishWithFade(collision));
            collision.gameObject.SetActive(false);
        }

    }

    IEnumerator ResetTractorPosition()
    {
        // Fade to black
        if (fadeScreen != null)
        {

            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        yield return new WaitForSeconds(0.5f);
        this.transform.position = Seed.position;
        this.transform.rotation = Seed.rotation;

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            instruction.SetActive(true);
            fadeScreen.gameObject.SetActive(false);
            this.GetComponent<Rigidbody>().drag = 0.01f;
        }
    }

    IEnumerator ResetTractorPosition1()
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        yield return new WaitForSeconds(0.5f);
        this.transform.position = trailer.position;
        this.transform.rotation = trailer.rotation;

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            instruction.SetActive(true);
            fadeScreen.gameObject.SetActive(false);
            this.GetComponent<Rigidbody>().drag = 0.01f;
        }
    }

    IEnumerator HandleFinishWithFade(Collider collision)
    {
        // Fade to black
        if (fadeScreen != null)
        {
            fadeScreen.gameObject.SetActive(true);
            fadeScreen.DOFade(1f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
        }

        // Wait a moment while screen is black
        yield return new WaitForSeconds(0.2f);

        // Activate finish sequence (without complete panel)
        cutscene.SetActive(true);
        rcccam.SetActive(false);
        canvas.SetActive(false);

        // Fade back to clear
        if (fadeScreen != null)
        {
            fadeScreen.DOFade(0f, fadeDuration);
            yield return new WaitForSeconds(fadeDuration);
            fadeScreen.gameObject.SetActive(false);
        }

        // Wait 6 seconds then activate complete panel
        yield return new WaitForSeconds(10f);
        complete.SetActive(true);
    }
    IEnumerator Finish()
    {
        yield return new WaitForSeconds(2f);
        fadeScreen.gameObject.SetActive(true);
        fadeScreen.DOFade(1f, fadeDuration);
        yield return new WaitForSeconds(fadeDuration);
        this.GetComponent<Rigidbody>().drag = 0.01f;
        instruction.SetActive(true);
        fadeScreen.gameObject.SetActive(false);
    }
}
